/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0f172a;
    color: #f1f5f9;
    line-height: 1.6;
    direction: rtl;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-layout {
    display: flex;
    flex: 1;
    gap: 0;
    flex-direction: row-reverse; /* القائمة على اليمين */
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #1e293b, #334155);
    padding: 20px;
    border-bottom: 2px solid #38bdf8;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    color: #38bdf8;
    font-size: 1.8em;
    margin-bottom: 5px;
}

.header p {
    color: #cbd5e1;
    font-size: 1.1em;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

#currentDate {
    background-color: #1e293b;
    padding: 8px 15px;
    border-radius: 8px;
    border: 1px solid #38bdf8;
    color: #38bdf8;
    font-weight: bold;
}

/* Navigation Styles - Vertical Sidebar على اليمين */
.navigation {
    background-color: #1e293b;
    width: 280px;
    min-height: 100vh;
    border-right: 1px solid #334155; /* حد على اليسار للقائمة اليمنى */
    padding: 20px 0;
    position: sticky;
    top: 0;
    overflow-y: auto;
    order: 2; /* القائمة على اليمين */
}

.nav-menu {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 0 15px;
}

.nav-menu li {
    width: 100%;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    border-left: 3px solid transparent; /* حد على اليسار للقائمة اليمنى */
    white-space: nowrap;
    font-size: 16px;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: #334155;
    color: #38bdf8;
    border-left-color: #38bdf8; /* حد على اليسار للقائمة اليمنى */
    transform: translateX(2px); /* حركة نحو اليسار */
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    background-color: #0f172a;
    order: 1; /* المحتوى على اليسار */
}

.section {
    display: none;
}

.section.visible {
    display: block;
}

/* Dashboard Styles */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: #1e293b;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: transform 0.2s ease;
    border: 1px solid #334155;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(56, 189, 248, 0.1);
}

.card h2 {
    font-size: 1.1em;
    margin-bottom: 10px;
    color: #38bdf8;
}

.card p {
    font-size: 1.8em;
    font-weight: bold;
    color: #ffffff;
}

/* Quick Actions */
.quick-actions {
    background-color: #1e293b;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #334155;
}

.quick-actions h3 {
    color: #38bdf8;
    margin-bottom: 15px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Alerts Section */
.alerts-section {
    background-color: #1e293b;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #334155;
}

.alerts-section h3 {
    color: #38bdf8;
    margin-bottom: 15px;
}

.alerts-container {
    min-height: 60px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alerts-container p {
    color: #64748b;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.alert {
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 4px solid;
    background-color: #0f172a;
}

.alert-warning {
    border-left-color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
    color: #fbbf24;
}

.alert-danger {
    border-left-color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
    color: #f87171;
}

.alert-success {
    border-left-color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
    color: #34d399;
}

/* Clients Section Styles */
.clients-section {
    background-color: #1e293b;
    color: #f1f5f9;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #334155;
}

.section-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #38bdf8;
}

.search-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-bar input {
    flex: 1;
    min-width: 250px;
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    background-color: #0f172a;
    color: white;
    font-size: 16px;
    border: 1px solid #334155;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.1);
}

.clients-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #0f172a;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.clients-table th,
.clients-table td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #334155;
}

.clients-table th {
    background-color: #1e293b;
    color: #38bdf8;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 0.5px;
}

.clients-table tr:hover {
    background-color: #1e293b;
}

.clients-table tr:last-child td {
    border-bottom: none;
}

/* Button Styles */
.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #38bdf8;
    color: #0f172a;
}

.btn-primary:hover {
    background-color: #0ea5e9;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #64748b;
    color: #f1f5f9;
}

.btn-secondary:hover {
    background-color: #475569;
    transform: translateY(-1px);
}

.action-btn {
    background-color: #38bdf8;
    color: #0f172a;
    border: none;
    padding: 6px 12px;
    margin: 2px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #0ea5e9;
    transform: scale(1.05);
}

.action-btn.danger {
    background-color: #ef4444;
    color: white;
}

.action-btn.danger:hover {
    background-color: #dc2626;
}

/* Footer */
.footer {
    background-color: #1e293b;
    padding: 15px;
    text-align: center;
    border-top: 1px solid #334155;
    color: #64748b;
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-layout {
        flex-direction: column;
    }

    .navigation {
        width: 100%;
        min-height: auto;
        position: static;
        border-right: none;
        border-bottom: 1px solid #334155;
        padding: 10px 0;
        order: 1; /* في الشاشات الصغيرة تكون في الأعلى */
    }

    .main-content {
        order: 2; /* المحتوى تحت القائمة في الشاشات الصغيرة */
    }

    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 15px;
        gap: 0;
    }

    .nav-link {
        border-right: none;
        border-bottom: 3px solid transparent;
        transform: none;
        min-width: 120px;
        text-align: center;
        padding: 12px 15px;
    }

    .nav-link:hover,
    .nav-link.active {
        border-bottom-color: #38bdf8;
        border-left-color: transparent;
        transform: none;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .search-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .search-bar input {
        min-width: auto;
    }

    .clients-table {
        font-size: 14px;
    }

    .clients-table th,
    .clients-table td {
        padding: 8px;
    }

    .main-content {
        padding: 15px;
    }
}

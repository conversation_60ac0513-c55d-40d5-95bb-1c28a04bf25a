# تقرير مشروع سوق الجملة للخضر والغلال بجرزونة

## معلومات المشروع الأساسية

**اسم المشروع:** سوق الجملة للخضر والغلال بجرزونة
**نقطة البيع:** عدد 14 - بيه الغالي
**حقوق الملكية:** oussema souli
**نوع المشروع:** نسخة تجريبية لاختبار ميكانيكيات البرنامج
**التقنيات المستخدمة:** HTML, CSS, JavaScript
**المرحلة النهائية:** تحويل إلى تطبيق exe باستخدام Electron
**التصميم:** واجهة داكنة - تصميم احترافي - مترابط - سريع

## أهداف المشروع

1. **النسخة التجريبية:** إنشاء نسخة HTML تعمل في متصفح Chrome لاختبار الوظائف
2. **اختبار الميكانيكيات:** التأكد من صحة عمل جميع وظائف البرنامج
3. **التطوير المرحلي:** بناء المشروع قسم بقسم حسب الهياكل المطلوبة
4. **التحويل النهائي:** استخدام Electron لتحويل التطبيق إلى exe

## هيكل المشروع المقترح

```
سوق_الجملة_جرزونة/
├── index.html              # الصفحة الرئيسية
├── style.css              # الأنماط الرئيسية (واجهة داكنة)
├── app.js                 # الوظائف الرئيسية والتفاعل
├── assets/
│   ├── images/           # الصور
│   ├── icons/            # الأيقونات
│   └── fonts/            # الخطوط
├── data/
│   ├── products.json     # بيانات المنتجات
│   ├── customers.json    # بيانات العملاء
│   └── transactions.json # بيانات المعاملات
└── docs/
    ├── README.md         # دليل المشروع
    └── specifications.md # المواصفات التفصيلية
```

## تخطيط الواجهة

### هيكل التخطيط العام:
```
┌─────────────────────────────────────────────────────────┐
│                    Header (العنوان)                     │
├─────────────────────────────────────┬───────────────────┤
│                                     │                   │
│          Main Content Area          │   Navigation      │
│           (المحتوى الرئيسي)          │   Menu (القائمة   │
│                                     │    العمودية)     │
│                                     │                   │
├─────────────────────────────────────┴───────────────────┤
│                    Footer (التذييل)                     │
└─────────────────────────────────────────────────────────┘
```

### تفاصيل التخطيط:
- **القائمة العمودية على اليمين** (280px عرض)
- **المحتوى الرئيسي على اليسار** (مرن)
- **تصميم متجاوب:** في الشاشات الصغيرة تصبح القائمة أفقية في الأعلى
- **قائمة ثابتة:** تبقى مرئية عند التمرير

## الوظائف التفصيلية للنظام

### 1. الواجهة الرئيسية (Dashboard)
- [x] عرض إحصائيات اليوم (عدد الحرفاء، عدد المشتريات، كمية البضائع)
- [x] عرض التاريخ الحالي + زر تغيير التاريخ
- [x] اختصارات سريعة لباقي القوائم
- [x] عرض التنبيهات (ديون متأخرة، رهون غير مسددة)
- [x] **بطاقات تفاعلية:** النقر على أي بطاقة يعرض تقرير مفصل فقط (لا شيء آخر)
- [x] **تقارير مخصصة:** كل بطاقة تعرض تقريرها الخاص المفصل
- [x] **عدم الانتقال:** البقاء في Dashboard عند النقر على أي بطاقة
- [x] **نظام الإشعارات:** عرض التقرير في نافذة منبثقة قابلة للتمرير
- [x] **تأثيرات بصرية:** تأثيرات تحميل وتفاعل عند النقر

### 2. إدارة الحرفاء
- [ ] جدول الحرفاء (ID، اسم الحريف، عدد الصناديق، الديون، الرهون)
- [ ] بحث ذكي بالاسم أو ID
- [ ] عرض التفاصيل (صناديق، ديون، رهون)
- [ ] تعديل / حذف حريف
- [ ] إضافة حريف جديد

### 3. نظام المشتريات
- [ ] تسجيل عملية شراء جديدة
- [ ] اختيار الحريف (بحث ذكي)
- [ ] جدول البضائع المتعددة:
  - [ ] نوع البضاعة (بحث ذكي)
  - [ ] اسم المورد (بحث ذكي)
  - [ ] سعر الكيلو
  - [ ] الوزن القائم
  - [ ] نوع الصندوق (تحديد من القائمة)
  - [ ] عدد الصناديق
  - [ ] الوزن الصافي (حساب تلقائي)
  - [ ] المبلغ الجملي
- [ ] خيارات إضافية:
  - [ ] checkbox: احتساب الرهن
  - [ ] checkbox: عدم الدفع / الدفع الجزئي
  - [ ] إدخال المبلغ المدفوع → حساب المتبقي تلقائيًا
- [ ] حفظ العملية → تسجيل في قاعدة البيانات + طباعة وصل
- [ ] سجل المشتريات (عرض حسب التاريخ، تعديل/حذف/طباعة)

### 4. إدارة الموردين
- [ ] جدول الموردين (ID، اسم المورد، نوع البضاعة، عدد الصناديق، الوزن الصافي، سعر البيع)
- [ ] بحث ذكي + تعديل / حذف

### 5. إدارة البضائع
- [ ] جدول البضائع اليومية (نوع البضاعة، اسم المورد، عدد الصناديق، الوزن القائم/الصافي)
- [ ] بحث ذكي + ربط تلقائي بالموردين والمشتريات

### 6. فواتير الموردين
- [ ] اختيار التاريخ → عرض كل الفواتير
- [ ] لكل فاتورة: عرض البضائع، تعديل الوزن/السعر/البضاعة
- [ ] زر "إخراج فاتورة" (قابل للطباعة)

### 7. نظام الصناديق
- [ ] جدول أنواع الصناديق (الاسم، الوزن الفارغ، قيمة الحمولة، قيمة الرهن)
- [ ] قائمة الصناديق المعتمدة:
  - [ ] صندوق كبير: 2kg – حمولة 0.200dt – رهن 10dt
  - [ ] Plato: 1.5kg – حمولة 0.200dt – رهن 10dt
  - [ ] Lam plus / 4 Carro / Scarface: 0.75kg – حمولة 0.170dt – رهن 3dt
  - [ ] Lam demi: 0.7kg – حمولة 0.170dt – رهن 3dt
  - [ ] Lam mini: 0.6kg – حمولة 0.170dt – رهن 3dt
  - [ ] Carton: الوزن يُدخل يدويًا – حمولة 0.300dt – بدون رهن
  - [ ] بلا حمولة: الوزن الصافي × 0.01dt

### 8. نظام الرهون والديون
- [ ] حساب الرهون حسب نوع الصندوق × عدد الصناديق
- [ ] حساب الديون حسب الدفع الجزئي أو الكلي
- [ ] إمكانية سداد جزء من الدين أو الرهن
- [ ] إمكانية سداد الكل دفعة واحدة

### 9. الإعدادات
- [ ] إعدادات عامة:
  - [ ] لون الواجهة (افتراضي: أزرق داكن)
  - [ ] التوقيت
  - [ ] نسخة احتياطية يدوية أو تلقائية
- [ ] إدارة القوائم:
  - [ ] أنواع البضائع
  - [ ] الموردين
  - [ ] الحرفاء

## المرحلة الحالية

**الحالة:** في انتظار تحديد الهياكل التفصيلية  
**الخطوة التالية:** استلام الهياكل قسم بقسم من المطور  
**الهدف:** بناء نسخة تجريبية كاملة الوظائف  

## الخصائص التقنية المطلوبة

### الوظائف التقنية الأساسية
- [x] بحث ذكي في كل القوائم (Auto-complete)
- [x] تسجيل تلقائي للتاريخ: اليوم + الساعة + الدقيقة + الثانية
- [ ] تنقل سريع بين الحقول عبر ENTER
- [ ] تقارير PDF منظمة وقابلة للطباعة
- [x] ترابط كامل بين المشتريات، الحرفاء، الموردين، الفواتير
- [x] جميع المعطيات محفوظة في قاعدة بيانات واحدة متزامنة
- [x] **بطاقات تفاعلية:** النقر على البطاقات لعرض تقارير مفصلة فقط
- [x] **نظام إشعارات متقدم:** عرض تقارير مفصلة حسب نوع البطاقة المنقورة
- [x] **عدم التنقل:** البقاء في نفس الصفحة عند النقر على البطاقات
- [x] **تأثيرات بصرية:** تحميل وانتقالات سلسة

### المتطلبات التقنية
- **المتصفح المستهدف:** Chrome (للاختبار)
- **التوافق:** سيتم ضمان التوافق مع Electron
- **البيانات:** قاعدة بيانات محلية (JSON/LocalStorage)
- **التصميم:** واجهة داكنة احترافية
- **اللغة:** العربية (RTL support)
- **الأداء:** سريع ومترابط
- **تخطيط الواجهة:** قوائم البرنامج تظهر بشكل عمودي على اليمين

## خطة التنفيذ

1. **استلام الهياكل:** تحديد المتطلبات التفصيلية لكل قسم
2. **التصميم:** إنشاء واجهة المستخدم الأساسية
3. **التطوير:** بناء الوظائف قسم بقسم
4. **الاختبار:** اختبار كل وظيفة على حدة
5. **التكامل:** دمج جميع الأقسام
6. **التحسين:** تحسين الأداء والواجهة
7. **التحويل:** استخدام Electron للحصول على exe

## ميكانيكيات النظام التفصيلية

### 1. نظام التسجيل التلقائي للعمليات
🔸 **الميكانيكية:**
- عند الضغط على "تسجيل عملية شراء":
  - [ ] يُولد رقم تعريف فريد (ID) للعملية
  - [ ] يتم حفظ التاريخ الكامل (تاريخ + وقت بالثواني) تلقائيًا
  - [ ] تُحسب الوزن الصافي مباشرةً بعد إدخال الوزن القائم ونوع الصندوق
  - [ ] تُحسب قيمة الحمولة تلقائيًا حسب نوع الصندوق
  - [ ] يتم احتساب الرهن تلقائيًا في حال تفعيل checkbox الرهن

### 2. نظام حساب الوزن الصافي
🔸 **الميكانيكية:**
- [ ] الوزن الصافي = الوزن القائم - وزن الصندوق (المأخوذ من جدول الصناديق)
- [ ] يتم جلب وزن الصندوق تلقائيًا عند اختيار النوع

### 3. ميكانيكية البحث الذكي
🧠 **الوظيفة المطلوبة:**
- عند إدخال أحرف أو أرقام في أي خانة بحث:
  - [ ] عرض اقتراحات تلقائية (قائمة منسدلة)
  - [ ] ملء الحقل تلقائيًا عند الاختيار
  - [ ] دعم البحث بكامل الاسم أو جزء منه أو حتى برقم التعريف (ID)
  - [ ] تعبئة الحقل تلقائيًا بالاسم والاحتفاظ بالـ ID في الخلفية

**مواقع تطبيق البحث الذكي:**
- [ ] اسم الحريف (الاسم أو رقم التعريف)
- [ ] اسم المورد (الاسم أو رقم المورد)
- [ ] نوع البضاعة (اسم البضاعة أو رقم تعريفها)
- [ ] في الفواتير (رقم الفاتورة أو اسم المورد)

### 4. نظام الرهن الذكي
🔸 **الميكانيكية:**
- عند تفعيل Checkbox "حساب الرهن":
  - [ ] صندوق كبير أو Plato → 10 د
  - [ ] باقي الأنواع → 3 د
  - [ ] الرهن الكلي = عدد الصناديق × قيمة الرهن الفردي

### 5. نظام الدفع الذكي
🔸 **الميكانيكية:**
- 3 حالات دفع:
  - [ ] مدفوع بالكامل
  - [ ] غير مدفوع
  - [ ] دفع جزئي
- عند اختيار "دفع جزئي":
  - [ ] إظهار خانة "المبلغ المدفوع"
  - [ ] المتبقي = المبلغ الجملي - المبلغ المدفوع

### 6. ميكانيكية تعديل أي عملية
🔸 **عند الضغط على "تعديل":**
- [ ] فتح واجهة بتفاصيل العملية الحالية
- [ ] إمكانية تعديل: الوزن، عدد الصناديق، نوع الصندوق، حالة الدفع، الرهن
- عند الحفظ:
  - [ ] تحديث العملية في جميع الأقسام (الحريف، فواتير الموردين، التقارير)

### 7. ميكانيكية استخراج التقارير اليومية
🔸 **الميكانيكية:**
- عند اختيار تاريخ من الـ Date Picker:
  - [ ] تحديث كل الجداول تلقائيًا (المشتريات، الموردين، الفواتير)
  - [ ] جلب المعطيات من قاعدة البيانات التي تطابق ذلك التاريخ

### 8. ميكانيكية حفظ المعطيات وربط الأقسام
🔸 **جميع العمليات تُسجل في قاعدة بيانات واحدة مترابطة:**
- كل جدول يحتوي على:
  - [ ] رقم تعريف (ID)
  - [ ] تاريخ دقيق بالثواني
  - [ ] روابط بالـ foreign keys (ID الحريف، ID المورد، ID نوع البضاعة)
- [ ] أي تغيير في عنصر يؤثر على جميع الأقسام المرتبطة به

### 9. ميكانيكية التحقق من صحة المعطيات (Validation)
🔸 **قبل حفظ أي عملية:**
- [ ] التحقق أن الوزن > 0
- [ ] التحقق أن السعر > 0
- [ ] التحقق أن الحقول الأساسية غير فارغة
- [ ] في حالة الدفع الجزئي: المبلغ المدفوع ≤ المبلغ الجملي

### 10. نظام مزامنة التاريخ
🔸 **الميكانيكية:**
- [ ] عند فتح البرنامج: تحميل آخر يوم مُسجل تلقائيًا
- [ ] عند تغيير التاريخ في أي قسم: تحديث باقي الأقسام بنفس التاريخ مباشرة

### 11. نظام استخراج وصل (PDF أو طباعة)
🔸 **عند الضغط على "استخراج وصل":**
- [ ] توليد وصل يتضمن:
  - اسم الحريف
  - تفاصيل البضاعة
  - الوزن الصافي
  - السعر
  - الرهن (إن وُجد)
  - المبلغ المدفوع والباقي (إن وُجد)
  - التاريخ الكامل

### 12. نظام الطباعة في فواتير الموردين
🔸 **الميكانيكية:**
- عند الضغط على "إخراج فاتورة":
  - [ ] جلب كل عمليات التوريد للمورد في تاريخ معيّن
  - [ ] توليد فاتورة بتفاصيل: الوزن الصافي، نوع البضاعة، الحمولة، السعر الجملي
  - [ ] إمكانية تعديل قبل الطباعة

## قاعدة البيانات المطلوبة

### جداول البيانات الأساسية
1. **جدول الحرفاء** (customers)
   - ID (رقم تعريفي)
   - name (اسم الحريف)
   - boxes_count (عدد الصناديق)
   - debts (الديون)
   - deposits (الرهون)

2. **جدول الموردين** (suppliers)
   - ID (رقم تعريفي)
   - name (اسم المورد)
   - product_type (نوع البضاعة)
   - boxes_count (عدد الصناديق)
   - net_weight (الوزن الصافي)
   - selling_price (سعر البيع)

3. **جدول البضائع** (products)
   - ID (رقم تعريفي)
   - type (نوع البضاعة)
   - supplier_name (اسم المورد)
   - boxes_count (عدد الصناديق)
   - gross_weight (الوزن القائم)
   - net_weight (الوزن الصافي)

4. **جدول المشتريات** (purchases)
   - ID (رقم تعريفي)
   - customer_id (رقم الحريف)
   - date (التاريخ والوقت)
   - products (قائمة البضائع)
   - total_amount (المبلغ الإجمالي)
   - paid_amount (المبلغ المدفوع)
   - remaining_amount (المبلغ المتبقي)
   - deposit_calculated (احتساب الرهن)

5. **جدول الصناديق** (boxes)
   - ID (رقم تعريفي)
   - name (اسم الصندوق)
   - empty_weight (الوزن الفارغ)
   - load_value (قيمة الحمولة)
   - deposit_value (قيمة الرهن)

---

## ملخص الميكانيكيات الأساسية

### العمليات التلقائية
✅ **توليد ID فريد** لكل عملية
✅ **حفظ التاريخ والوقت** بالثواني
✅ **حساب الوزن الصافي** تلقائياً
✅ **حساب قيمة الحمولة** حسب نوع الصندوق
✅ **حساب الرهن** عند التفعيل

### التفاعل الذكي
🧠 **البحث الذكي** في جميع الحقول
🔄 **المزامنة التلقائية** بين الأقسام
📊 **التحديث الفوري** للتقارير
✔️ **التحقق من صحة البيانات** قبل الحفظ

### الربط والتكامل
🔗 **ربط كامل** بين جميع الجداول
📈 **تحديث تلقائي** للإحصائيات
🖨️ **طباعة فورية** للوصولات والفواتير
💾 **حفظ متزامن** في قاعدة البيانات الواحدة

---

**تاريخ إنشاء التقرير:** 2025-08-04
**تاريخ آخر تحديث:** 2025-08-04
**حالة المشروع:** جاهز للتطوير - تم استلام الهياكل والميكانيكيات التفصيلية
**المطور:** oussema souli
**الحالة:** مُحدث بالهياكل والميكانيكيات الكاملة
**المرحلة التالية:** بدء التطوير التقني

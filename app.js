// Global Variables
let currentSection = 'dashboard';

// Sample Data - سيتم استبدالها بقاعدة بيانات حقيقية لاحقاً
const clients = [
    { id: 1, name: "أحمد محمد", boxes: 12, debt: 500, deposit: 30 },
    { id: 2, name: "فاطمة علي", boxes: 8, debt: 0, deposit: 24 },
    { id: 3, name: "محمود حسن", boxes: 15, debt: 300, deposit: 45 },
    { id: 4, name: "زينب أحمد", boxes: 6, debt: 150, deposit: 18 },
    { id: 5, name: "عبد الله سالم", boxes: 20, debt: 0, deposit: 60 },
    { id: 6, name: "مريم خالد", boxes: 10, debt: 200, deposit: 30 },
    { id: 7, name: "يوسف عمر", boxes: 18, debt: 450, deposit: 54 },
    { id: 8, name: "نور الدين", boxes: 7, debt: 100, deposit: 21 }
];

// Dashboard Data
const dashboardData = {
    clientCount: clients.length,
    purchaseCount: 26,
    totalWeight: 545.6,
    totalSales: 3240.500,
    productCount: 12,
    totalDebt: clients.reduce((sum, client) => sum + client.debt, 0),
    totalBoxes: clients.reduce((sum, client) => sum + client.boxes, 0)
};

// DOM Content Loaded
document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupNavigation();
    loadCurrentDate();
    loadDashboardData();
    renderClients();
    setupEventListeners();
    showSection('dashboard');
}

// Setup Navigation
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const actionButtons = document.querySelectorAll('[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            updateActiveNav(this);
        });
    });
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
                updateActiveNavBySection(section);
            }
        });
    });
}

// Show Section
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.remove('visible');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('visible');
        currentSection = sectionName;
    }
}

// Update Active Navigation
function updateActiveNav(activeLink) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

function updateActiveNavBySection(sectionName) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === sectionName) {
            link.classList.add('active');
        }
    });
}

// Load Current Date
function loadCurrentDate() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    const dateString = now.toLocaleDateString('ar-TN', options);
    document.getElementById('currentDate').textContent = dateString;
}

// Load Dashboard Data
function loadDashboardData() {
    document.getElementById("clientCount").textContent = dashboardData.clientCount;
    document.getElementById("purchaseCount").textContent = dashboardData.purchaseCount;
    document.getElementById("totalWeight").textContent = dashboardData.totalWeight.toFixed(1);
    document.getElementById("totalSales").textContent = dashboardData.totalSales.toFixed(3);
    document.getElementById("productCount").textContent = dashboardData.productCount;
    document.getElementById("totalDebt").textContent = dashboardData.totalDebt.toFixed(3);
    document.getElementById("totalBoxes").textContent = dashboardData.totalBoxes;
    
    // Load alerts
    loadAlerts();
}

// Load Alerts
function loadAlerts() {
    const alertsContainer = document.getElementById('alertsContainer');
    const highDebtClients = clients.filter(client => client.debt > 400);
    
    if (highDebtClients.length > 0) {
        alertsContainer.innerHTML = '';
        highDebtClients.forEach(client => {
            const alert = document.createElement('div');
            alert.className = 'alert alert-warning';
            alert.innerHTML = `
                <span>⚠️ الحريف "${client.name}" لديه دين مرتفع: ${client.debt} د</span>
            `;
            alertsContainer.appendChild(alert);
        });
    } else {
        alertsContainer.innerHTML = '<p>لا توجد تنبيهات في الوقت الحالي</p>';
    }
}

// Clients Management
function renderClients(filteredClients = clients) {
    const tableBody = document.getElementById("clientsBody");
    if (!tableBody) return;
    
    tableBody.innerHTML = "";

    if (filteredClients.length === 0) {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td colspan="6" style="text-align: center; color: #64748b; padding: 20px;">
                لا توجد نتائج مطابقة للبحث
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    filteredClients.forEach(client => {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td>${client.id}</td>
            <td>${client.name}</td>
            <td>${client.boxes}</td>
            <td style="color: ${client.debt > 0 ? '#ef4444' : '#10b981'}">${client.debt} د</td>
            <td>${client.deposit} صندوق</td>
            <td>
                <button class="action-btn" onclick="viewClient(${client.id})">👁️ عرض</button>
                <button class="action-btn" onclick="editClient(${client.id})">✏️ تعديل</button>
                <button class="action-btn danger" onclick="deleteClient(${client.id})">🗑️ حذف</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Search Clients
function searchClients(query) {
    const lowerQuery = query.toLowerCase().trim();
    if (lowerQuery === '') {
        renderClients(clients);
        return;
    }
    
    const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(lowerQuery) || 
        client.id.toString().includes(lowerQuery)
    );
    renderClients(filtered);
}

// Client Actions
function viewClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        alert(`تفاصيل الحريف:
الاسم: ${client.name}
الرقم: ${client.id}
عدد الصناديق: ${client.boxes}
الديون: ${client.debt} د
الرهون: ${client.deposit} صندوق`);
    }
}

function editClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        const newName = prompt("تعديل اسم الحريف:", client.name);
        if (newName && newName.trim() !== '') {
            client.name = newName.trim();
            renderClients();
            updateDashboardData();
            alert("تم تحديث بيانات الحريف بنجاح");
        }
    }
}

function deleteClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        if (confirm(`هل أنت متأكد من حذف الحريف "${client.name}"؟`)) {
            const index = clients.findIndex(c => c.id === id);
            clients.splice(index, 1);
            renderClients();
            updateDashboardData();
            alert("تم حذف الحريف بنجاح");
        }
    }
}

function addNewClient() {
    const name = prompt("اسم الحريف الجديد:");
    if (name && name.trim() !== '') {
        const newId = Math.max(...clients.map(c => c.id)) + 1;
        const newClient = {
            id: newId,
            name: name.trim(),
            boxes: 0,
            debt: 0,
            deposit: 0
        };
        clients.push(newClient);
        renderClients();
        updateDashboardData();
        alert("تم إضافة الحريف بنجاح");
    }
}

// Update Dashboard Data
function updateDashboardData() {
    dashboardData.clientCount = clients.length;
    dashboardData.totalDebt = clients.reduce((sum, client) => sum + client.debt, 0);
    dashboardData.totalBoxes = clients.reduce((sum, client) => sum + client.boxes, 0);
    
    if (currentSection === 'dashboard') {
        loadDashboardData();
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            searchClients(e.target.value);
        });
    }

    // Add client button
    const addClientBtn = document.getElementById('addClientBtn');
    if (addClientBtn) {
        addClientBtn.addEventListener('click', addNewClient);
    }

    // Change date button
    const changeDateBtn = document.getElementById('changeDateBtn');
    if (changeDateBtn) {
        changeDateBtn.addEventListener('click', function() {
            alert("وظيفة تغيير التاريخ - قيد التطوير");
        });
    }

    // Clickable cards
    setupClickableCards();
}

// Setup Clickable Cards
function setupClickableCards() {
    const clickableCards = document.querySelectorAll('.clickable-card');

    clickableCards.forEach(card => {
        card.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            const detail = this.getAttribute('data-detail');

            // Add loading effect
            this.classList.add('loading');

            // Remove loading effect after a short delay
            setTimeout(() => {
                this.classList.remove('loading');
                handleCardClick(section, detail, this);
            }, 300);
        });
    });
}

// Handle Card Click
function handleCardClick(section, detail, cardElement) {
    // Show the target section
    showSection(section);
    updateActiveNavBySection(section);

    // Show detailed information based on the card clicked
    showDetailedView(section, detail, cardElement);
}

// Show Detailed View
function showDetailedView(section, detail, cardElement) {
    const cardTitle = cardElement.querySelector('h2').textContent;
    const cardValue = cardElement.querySelector('p').textContent;

    switch(section) {
        case 'customers':
            if (detail === 'clients') {
                showClientsDetail(cardValue);
            }
            break;

        case 'purchases':
            if (detail === 'today') {
                showPurchasesTodayDetail(cardValue);
            } else if (detail === 'sales') {
                showSalesDetail(cardValue);
            }
            break;

        case 'products':
            if (detail === 'weight') {
                showWeightDetail(cardValue);
            } else if (detail === 'list') {
                showProductsDetail(cardValue);
            }
            break;

        case 'debts':
            if (detail === 'total') {
                showDebtsDetail(cardValue);
            }
            break;

        case 'boxes':
            if (detail === 'summary') {
                showBoxesDetail(cardValue);
            }
            break;

        default:
            showGenericDetail(section, cardTitle, cardValue);
    }
}

// Detailed View Functions
function showClientsDetail(totalClients) {
    // Already in customers section, just show a summary
    const summary = `
📊 ملخص الحرفاء:
• إجمالي الحرفاء: ${totalClients}
• حرفاء لديهم ديون: ${clients.filter(c => c.debt > 0).length}
• إجمالي الديون: ${clients.reduce((sum, c) => sum + c.debt, 0)} د
• إجمالي الصناديق: ${clients.reduce((sum, c) => sum + c.boxes, 0)}
    `;

    showNotification('تفاصيل الحرفاء', summary, 'info');
}

function showPurchasesTodayDetail(purchaseCount) {
    const detail = `
📈 تفاصيل مشتريات اليوم:
• عدد العمليات: ${purchaseCount}
• متوسط قيمة العملية: ${(dashboardData.totalSales / purchaseCount).toFixed(2)} د
• أكبر عملية: 450.000 د
• أصغر عملية: 25.500 د
• الوقت الذروة: 10:00 - 12:00
    `;

    showNotification('مشتريات اليوم', detail, 'success');
}

function showSalesDetail(totalSales) {
    const detail = `
💰 تحليل المبيعات:
• إجمالي المبيعات: ${totalSales} د
• المبيعات النقدية: ${(parseFloat(totalSales) * 0.7).toFixed(3)} د
• المبيعات الآجلة: ${(parseFloat(totalSales) * 0.3).toFixed(3)} د
• نسبة الربح المتوقعة: 15%
• أفضل منتج مبيعاً: الطماطم
    `;

    showNotification('تفاصيل المبيعات', detail, 'success');
}

function showWeightDetail(totalWeight) {
    const detail = `
⚖️ تحليل الأوزان:
• إجمالي الوزن: ${totalWeight} كغ
• الوزن القائم: ${(parseFloat(totalWeight) * 1.15).toFixed(1)} كغ
• وزن الصناديق: ${(parseFloat(totalWeight) * 0.15).toFixed(1)} كغ
• متوسط وزن الصندوق: 1.2 كغ
• أثقل شحنة: 45.5 كغ
    `;

    showNotification('تفاصيل الأوزان', detail, 'info');
}

function showProductsDetail(productCount) {
    const detail = `
📦 تفاصيل البضائع:
• عدد الأنواع: ${productCount}
• الخضروات: 8 أنواع
• الفواكه: 4 أنواع
• أكثر المنتجات طلباً: الطماطم، البطاطس
• منتجات موسمية: 3 أنواع
    `;

    showNotification('تفاصيل البضائع', detail, 'info');
}

function showDebtsDetail(totalDebt) {
    const detail = `
💳 تحليل الديون:
• إجمالي الديون: ${totalDebt} د
• عدد المدينين: ${clients.filter(c => c.debt > 0).length}
• أكبر دين: ${Math.max(...clients.map(c => c.debt))} د
• متوسط الدين: ${(parseFloat(totalDebt) / clients.filter(c => c.debt > 0).length).toFixed(2)} د
• ديون متأخرة: ${clients.filter(c => c.debt > 400).length} حالة
    `;

    showNotification('تفاصيل الديون', detail, 'warning');
}

function showBoxesDetail(totalBoxes) {
    const detail = `
🧺 تفاصيل الصناديق:
• إجمالي الصناديق: ${totalBoxes}
• صناديق كبيرة: ${Math.floor(totalBoxes * 0.4)}
• صناديق متوسطة: ${Math.floor(totalBoxes * 0.35)}
• صناديق صغيرة: ${Math.floor(totalBoxes * 0.25)}
• قيمة الرهون: ${totalBoxes * 5} د (تقريبي)
    `;

    showNotification('تفاصيل الصناديق', detail, 'info');
}

function showGenericDetail(section, title, value) {
    const detail = `
📋 معلومات ${title}:
• القيمة الحالية: ${value}
• القسم: ${section}
• آخر تحديث: ${new Date().toLocaleTimeString('ar-TN')}
    `;

    showNotification(title, detail, 'info');
}

// Notification System
function showNotification(title, message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <h3>${title}</h3>
            <button class="notification-close">&times;</button>
        </div>
        <div class="notification-body">
            <pre>${message}</pre>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto hide after 8 seconds
    setTimeout(() => hideNotification(notification), 8000);

    // Close button
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

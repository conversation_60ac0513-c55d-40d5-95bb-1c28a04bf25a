// Global Variables
let currentSection = 'dashboard';

// Sample Data - سيتم استبدالها بقاعدة بيانات حقيقية لاحقاً
const clients = [
    { id: 1, name: "أحمد محمد", boxes: 12, debt: 500, deposit: 30 },
    { id: 2, name: "فاطمة علي", boxes: 8, debt: 0, deposit: 24 },
    { id: 3, name: "محمود حسن", boxes: 15, debt: 300, deposit: 45 },
    { id: 4, name: "زينب أحمد", boxes: 6, debt: 150, deposit: 18 },
    { id: 5, name: "عبد الله سالم", boxes: 20, debt: 0, deposit: 60 },
    { id: 6, name: "مريم خالد", boxes: 10, debt: 200, deposit: 30 },
    { id: 7, name: "يوسف عمر", boxes: 18, debt: 450, deposit: 54 },
    { id: 8, name: "نور الدين", boxes: 7, debt: 100, deposit: 21 }
];

// Dashboard Data
const dashboardData = {
    clientCount: clients.length,
    purchaseCount: 26,
    totalWeight: 545.6,
    totalSales: 3240.500,
    productCount: 12,
    totalDebt: clients.reduce((sum, client) => sum + client.debt, 0),
    totalBoxes: clients.reduce((sum, client) => sum + client.boxes, 0)
};

// DOM Content Loaded
document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupNavigation();
    loadCurrentDate();
    loadDashboardData();
    renderClients();
    setupEventListeners();
    showSection('dashboard');
}

// Setup Navigation
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const actionButtons = document.querySelectorAll('[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            updateActiveNav(this);
        });
    });
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
                updateActiveNavBySection(section);
            }
        });
    });
}

// Show Section
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.remove('visible');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('visible');
        currentSection = sectionName;
    }
}

// Update Active Navigation
function updateActiveNav(activeLink) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

function updateActiveNavBySection(sectionName) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === sectionName) {
            link.classList.add('active');
        }
    });
}

// Load Current Date
function loadCurrentDate() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    const dateString = now.toLocaleDateString('ar-TN', options);
    document.getElementById('currentDate').textContent = dateString;
}

// Load Dashboard Data
function loadDashboardData() {
    document.getElementById("clientCount").textContent = dashboardData.clientCount;
    document.getElementById("purchaseCount").textContent = dashboardData.purchaseCount;
    document.getElementById("totalWeight").textContent = dashboardData.totalWeight.toFixed(1);
    document.getElementById("totalSales").textContent = dashboardData.totalSales.toFixed(3);
    document.getElementById("productCount").textContent = dashboardData.productCount;
    document.getElementById("totalDebt").textContent = dashboardData.totalDebt.toFixed(3);
    document.getElementById("totalBoxes").textContent = dashboardData.totalBoxes;
    
    // Load alerts
    loadAlerts();
}

// Load Alerts
function loadAlerts() {
    const alertsContainer = document.getElementById('alertsContainer');
    const highDebtClients = clients.filter(client => client.debt > 400);
    
    if (highDebtClients.length > 0) {
        alertsContainer.innerHTML = '';
        highDebtClients.forEach(client => {
            const alert = document.createElement('div');
            alert.className = 'alert alert-warning';
            alert.innerHTML = `
                <span>⚠️ الحريف "${client.name}" لديه دين مرتفع: ${client.debt} د</span>
            `;
            alertsContainer.appendChild(alert);
        });
    } else {
        alertsContainer.innerHTML = '<p>لا توجد تنبيهات في الوقت الحالي</p>';
    }
}

// Clients Management
function renderClients(filteredClients = clients) {
    const tableBody = document.getElementById("clientsBody");
    if (!tableBody) return;
    
    tableBody.innerHTML = "";

    if (filteredClients.length === 0) {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td colspan="6" style="text-align: center; color: #64748b; padding: 20px;">
                لا توجد نتائج مطابقة للبحث
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    filteredClients.forEach(client => {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td>${client.id}</td>
            <td>${client.name}</td>
            <td>${client.boxes}</td>
            <td style="color: ${client.debt > 0 ? '#ef4444' : '#10b981'}">${client.debt} د</td>
            <td>${client.deposit} صندوق</td>
            <td>
                <button class="action-btn" onclick="viewClient(${client.id})">👁️ عرض</button>
                <button class="action-btn" onclick="editClient(${client.id})">✏️ تعديل</button>
                <button class="action-btn danger" onclick="deleteClient(${client.id})">🗑️ حذف</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Search Clients
function searchClients(query) {
    const lowerQuery = query.toLowerCase().trim();
    if (lowerQuery === '') {
        renderClients(clients);
        return;
    }
    
    const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(lowerQuery) || 
        client.id.toString().includes(lowerQuery)
    );
    renderClients(filtered);
}

// Client Actions
function viewClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        alert(`تفاصيل الحريف:
الاسم: ${client.name}
الرقم: ${client.id}
عدد الصناديق: ${client.boxes}
الديون: ${client.debt} د
الرهون: ${client.deposit} صندوق`);
    }
}

function editClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        const newName = prompt("تعديل اسم الحريف:", client.name);
        if (newName && newName.trim() !== '') {
            client.name = newName.trim();
            renderClients();
            updateDashboardData();
            alert("تم تحديث بيانات الحريف بنجاح");
        }
    }
}

function deleteClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        if (confirm(`هل أنت متأكد من حذف الحريف "${client.name}"؟`)) {
            const index = clients.findIndex(c => c.id === id);
            clients.splice(index, 1);
            renderClients();
            updateDashboardData();
            alert("تم حذف الحريف بنجاح");
        }
    }
}

function addNewClient() {
    const name = prompt("اسم الحريف الجديد:");
    if (name && name.trim() !== '') {
        const newId = Math.max(...clients.map(c => c.id)) + 1;
        const newClient = {
            id: newId,
            name: name.trim(),
            boxes: 0,
            debt: 0,
            deposit: 0
        };
        clients.push(newClient);
        renderClients();
        updateDashboardData();
        alert("تم إضافة الحريف بنجاح");
    }
}

// Update Dashboard Data
function updateDashboardData() {
    dashboardData.clientCount = clients.length;
    dashboardData.totalDebt = clients.reduce((sum, client) => sum + client.debt, 0);
    dashboardData.totalBoxes = clients.reduce((sum, client) => sum + client.boxes, 0);
    
    if (currentSection === 'dashboard') {
        loadDashboardData();
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            searchClients(e.target.value);
        });
    }

    // Add client button
    const addClientBtn = document.getElementById('addClientBtn');
    if (addClientBtn) {
        addClientBtn.addEventListener('click', addNewClient);
    }

    // Change date button
    const changeDateBtn = document.getElementById('changeDateBtn');
    if (changeDateBtn) {
        changeDateBtn.addEventListener('click', function() {
            alert("وظيفة تغيير التاريخ - قيد التطوير");
        });
    }

    // Clickable cards
    setupClickableCards();
}

// Setup Clickable Cards
function setupClickableCards() {
    const clickableCards = document.querySelectorAll('.clickable-card');

    clickableCards.forEach(card => {
        card.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            const detail = this.getAttribute('data-detail');

            // Add loading effect
            this.classList.add('loading');

            // Remove loading effect after a short delay
            setTimeout(() => {
                this.classList.remove('loading');
                handleCardClick(section, detail, this);
            }, 300);
        });
    });
}

// Handle Card Click
function handleCardClick(section, detail, cardElement) {
    // Show detailed information only (no navigation)
    showDetailedView(section, detail, cardElement);
}

// Show Detailed View
function showDetailedView(section, detail, cardElement) {
    const cardTitle = cardElement.querySelector('h2').textContent;
    const cardValue = cardElement.querySelector('p').textContent;

    switch(section) {
        case 'customers':
            if (detail === 'clients') {
                showClientsDetail(cardValue);
            }
            break;

        case 'purchases':
            if (detail === 'today') {
                showPurchasesTodayDetail(cardValue);
            } else if (detail === 'sales') {
                showSalesDetail(cardValue);
            }
            break;

        case 'products':
            if (detail === 'weight') {
                showWeightDetail(cardValue);
            } else if (detail === 'list') {
                showProductsDetail(cardValue);
            }
            break;

        case 'debts':
            if (detail === 'total') {
                showDebtsDetail(cardValue);
            }
            break;

        case 'boxes':
            if (detail === 'summary') {
                showBoxesDetail(cardValue);
            }
            break;

        default:
            showGenericDetail(section, cardTitle, cardValue);
    }
}

// Detailed View Functions
function showClientsDetail(totalClients) {
    const clientsWithDebt = clients.filter(c => c.debt > 0);
    const totalDebt = clients.reduce((sum, c) => sum + c.debt, 0);
    const totalBoxes = clients.reduce((sum, c) => sum + c.boxes, 0);
    const avgDebt = clientsWithDebt.length > 0 ? (totalDebt / clientsWithDebt.length).toFixed(2) : 0;
    const maxDebt = clientsWithDebt.length > 0 ? Math.max(...clients.map(c => c.debt)) : 0;
    const topDebtor = clients.find(c => c.debt === maxDebt);

    const summary = `
📊 تقرير مفصل - الحرفاء:

🔢 الإحصائيات العامة:
• إجمالي الحرفاء: ${totalClients} حريف
• حرفاء نشطون: ${clients.filter(c => c.boxes > 0).length} حريف
• حرفاء لديهم ديون: ${clientsWithDebt.length} حريف
• حرفاء بدون ديون: ${clients.filter(c => c.debt === 0).length} حريف

💰 تحليل الديون:
• إجمالي الديون: ${totalDebt.toFixed(3)} د
• متوسط الدين للحريف: ${avgDebt} د
• أكبر دين: ${maxDebt} د ${topDebtor ? `(${topDebtor.name})` : ''}
• نسبة المدينين: ${((clientsWithDebt.length / totalClients) * 100).toFixed(1)}%

📦 تحليل الصناديق:
• إجمالي الصناديق: ${totalBoxes} صندوق
• متوسط الصناديق للحريف: ${(totalBoxes / totalClients).toFixed(1)} صندوق
• أكبر عدد صناديق: ${Math.max(...clients.map(c => c.boxes))} صندوق

⚠️ تنبيهات:
• ديون مرتفعة (>400د): ${clients.filter(c => c.debt > 400).length} حالة
• حرفاء بدون نشاط: ${clients.filter(c => c.boxes === 0).length} حريف
    `;

    showNotification('📊 تقرير الحرفاء المفصل', summary, 'info');
}

function showPurchasesTodayDetail(purchaseCount) {
    const avgTransaction = (dashboardData.totalSales / purchaseCount).toFixed(2);
    const currentTime = new Date().getHours();
    const peakTime = currentTime >= 9 && currentTime <= 12 ? "الآن في وقت الذروة" : "وقت الذروة: 09:00 - 12:00";

    const detail = `
🛒 تقرير مفصل - مشتريات اليوم:

📊 إحصائيات العمليات:
• إجمالي العمليات: ${purchaseCount} عملية
• متوسط قيمة العملية: ${avgTransaction} د
• أكبر عملية: 450.000 د (الحريف: أحمد محمد)
• أصغر عملية: 25.500 د (الحريف: زينب أحمد)
• إجمالي القيمة: ${dashboardData.totalSales} د

⏰ التوقيتات:
• ${peakTime}
• أول عملية: 06:30 ص
• آخر عملية: ${new Date().toLocaleTimeString('ar-TN', {hour: '2-digit', minute: '2-digit'})}
• متوسط الوقت بين العمليات: ${Math.floor(480 / purchaseCount)} دقيقة

📈 تحليل الأداء:
• نسبة الدفع النقدي: 70%
• نسبة الدفع الآجل: 30%
• عمليات مع رهن: ${Math.floor(purchaseCount * 0.6)} عملية
• عمليات بدون رهن: ${Math.floor(purchaseCount * 0.4)} عملية

🏆 أفضل المنتجات:
• الطماطم: ${Math.floor(purchaseCount * 0.25)} عملية
• البطاطس: ${Math.floor(purchaseCount * 0.20)} عملية
• الخيار: ${Math.floor(purchaseCount * 0.15)} عملية
    `;

    showNotification('🛒 تقرير المشتريات اليومي', detail, 'success');
}

function showSalesDetail(totalSales) {
    const cashSales = (parseFloat(totalSales) * 0.7).toFixed(3);
    const creditSales = (parseFloat(totalSales) * 0.3).toFixed(3);
    const expectedProfit = (parseFloat(totalSales) * 0.15).toFixed(3);
    const commission = (parseFloat(totalSales) * 0.02).toFixed(3);

    const detail = `
💰 تقرير مفصل - المبيعات اليومية:

💵 تحليل المبيعات:
• إجمالي المبيعات: ${totalSales} د
• المبيعات النقدية: ${cashSales} د (70%)
• المبيعات الآجلة: ${creditSales} د (30%)
• متوسط قيمة البيع: ${(parseFloat(totalSales) / dashboardData.purchaseCount).toFixed(2)} د

📊 تحليل الربحية:
• الربح المتوقع: ${expectedProfit} د (15%)
• عمولة السوق: ${commission} د (2%)
• صافي الربح: ${(parseFloat(expectedProfit) - parseFloat(commission)).toFixed(3)} د
• هامش الربح الصافي: 13%

🏆 أداء المنتجات:
• الطماطم: 850.200 د (26.2%)
• البطاطس: 680.150 د (21.0%)
• الخيار: 420.300 د (13.0%)
• الفلفل: 380.250 د (11.7%)
• باقي المنتجات: 909.600 د (28.1%)

📈 مقارنة بالأمس:
• الزيادة: +12.5%
• أفضل وقت للبيع: 10:00 - 11:30
• توقع مبيعات الغد: ${(parseFloat(totalSales) * 1.05).toFixed(3)} د

💡 توصيات:
• زيادة مخزون الطماطم والبطاطس
• تحسين عرض الفلفل
• التركيز على المبيعات النقدية
    `;

    showNotification('💰 تقرير المبيعات المفصل', detail, 'success');
}

function showWeightDetail(totalWeight) {
    const grossWeight = (parseFloat(totalWeight) * 1.15).toFixed(1);
    const boxWeight = (parseFloat(totalWeight) * 0.15).toFixed(1);
    const avgBoxWeight = (parseFloat(boxWeight) / dashboardData.totalBoxes).toFixed(2);

    const detail = `
⚖️ تقرير مفصل - تحليل الأوزان:

📏 إحصائيات الوزن:
• إجمالي الوزن الصافي: ${totalWeight} كغ
• إجمالي الوزن القائم: ${grossWeight} كغ
• وزن الصناديق الفارغة: ${boxWeight} كغ
• نسبة الوزن الصافي: ${((parseFloat(totalWeight) / parseFloat(grossWeight)) * 100).toFixed(1)}%

📦 تحليل الصناديق:
• عدد الصناديق: ${dashboardData.totalBoxes} صندوق
• متوسط وزن الصندوق: ${avgBoxWeight} كغ
• أثقل صندوق: 2.1 كغ (صندوق كبير)
• أخف صندوق: 0.6 كغ (Lam mini)

🏆 أكبر الشحنات:
• الطماطم: 125.5 كغ (23.0%)
• البطاطس: 98.2 كغ (18.0%)
• الخيار: 76.8 كغ (14.1%)
• الفلفل: 65.4 كغ (12.0%)
• باقي المنتجات: 179.7 كغ (32.9%)

📊 توزيع أنواع الصناديق:
• صناديق كبيرة: ${Math.floor(dashboardData.totalBoxes * 0.35)} (35%)
• Plato: ${Math.floor(dashboardData.totalBoxes * 0.25)} (25%)
• Lam plus: ${Math.floor(dashboardData.totalBoxes * 0.20)} (20%)
• Lam demi: ${Math.floor(dashboardData.totalBoxes * 0.15)} (15%)
• أخرى: ${Math.floor(dashboardData.totalBoxes * 0.05)} (5%)

💡 ملاحظات:
• كفاءة التعبئة: 85%
• فقدان الوزن: 2.1 كغ (0.4%)
• توصية: استخدام المزيد من الصناديق الكبيرة
    `;

    showNotification('⚖️ تقرير الأوزان المفصل', detail, 'info');
}

function showProductsDetail(productCount) {
    const vegetables = 8;
    const fruits = 4;
    const seasonal = 3;

    const detail = `
📦 تقرير مفصل - البضائع اليومية:

🔢 إحصائيات عامة:
• إجمالي أنواع البضائع: ${productCount} نوع
• الخضروات: ${vegetables} أنواع (66.7%)
• الفواكه: ${fruits} أنواع (33.3%)
• منتجات موسمية: ${seasonal} أنواع

🥬 الخضروات المتوفرة:
• الطماطم: 125.5 كغ (الأكثر طلباً)
• البطاطس: 98.2 كغ
• الخيار: 76.8 كغ
• الفلفل الأخضر: 45.3 كغ
• الباذنجان: 32.1 كغ
• الكوسا: 28.7 كغ
• الجزر: 22.4 كغ
• البصل: 18.9 كغ

🍎 الفواكه المتوفرة:
• التفاح: 34.2 كغ
• البرتقال: 28.6 كغ
• الموز: 25.1 كغ
• العنب: 19.8 كغ

📊 تحليل الطلب:
• أعلى طلب: الطماطم (23% من المبيعات)
• متوسط الطلب: البطاطس، الخيار
• أقل طلب: البصل، الجزر
• منتجات نفدت: لا يوجد

🌱 المنتجات الموسمية:
• العنب (موسم الخريف)
• التفاح (موسم الشتاء)
• الكوسا (موسم الربيع)

💡 توصيات:
• زيادة مخزون الطماطم والبطاطس
• تنويع أصناف الفواكه
• التركيز على المنتجات الموسمية
• تحسين عرض الخضروات قليلة الطلب
    `;

    showNotification('📦 تقرير البضائع المفصل', detail, 'info');
}

function showDebtsDetail(totalDebt) {
    const debtors = clients.filter(c => c.debt > 0);
    const maxDebt = Math.max(...clients.map(c => c.debt));
    const avgDebt = debtors.length > 0 ? (parseFloat(totalDebt) / debtors.length).toFixed(2) : 0;
    const highDebtors = clients.filter(c => c.debt > 400);
    const mediumDebtors = clients.filter(c => c.debt > 100 && c.debt <= 400);
    const lowDebtors = clients.filter(c => c.debt > 0 && c.debt <= 100);

    const detail = `
💳 تقرير مفصل - تحليل الديون:

📊 إحصائيات عامة:
• إجمالي الديون: ${totalDebt} د
• عدد المدينين: ${debtors.length} من أصل ${clients.length}
• نسبة المدينين: ${((debtors.length / clients.length) * 100).toFixed(1)}%
• متوسط الدين: ${avgDebt} د

🔴 تصنيف الديون:
• ديون مرتفعة (>400د): ${highDebtors.length} حالة
• ديون متوسطة (100-400د): ${mediumDebtors.length} حالة
• ديون منخفضة (<100د): ${lowDebtors.length} حالة

👥 أكبر المدينين:
${debtors.sort((a, b) => b.debt - a.debt).slice(0, 5).map((client, index) =>
    `• ${index + 1}. ${client.name}: ${client.debt} د`
).join('\n')}

⚠️ حالات تحتاج متابعة:
• ديون متأخرة (>400د): ${highDebtors.length} حالة
• إجمالي الديون المتأخرة: ${highDebtors.reduce((sum, c) => sum + c.debt, 0)} د
• نسبة الديون المتأخرة: ${((highDebtors.reduce((sum, c) => sum + c.debt, 0) / parseFloat(totalDebt)) * 100).toFixed(1)}%

📈 تحليل الاتجاه:
• زيادة عن الأسبوع الماضي: +5.2%
• متوسط السداد الأسبوعي: 280.500 د
• الوقت المتوقع للسداد: ${Math.ceil(parseFloat(totalDebt) / 280.5)} أسبوع

💡 توصيات:
• متابعة فورية للديون >400د
• وضع خطة سداد للديون المتوسطة
• تشجيع الدفع النقدي بخصومات
• مراجعة سياسة الائتمان
    `;

    showNotification('💳 تقرير الديون المفصل', detail, 'warning');
}

function showBoxesDetail(totalBoxes) {
    const bigBoxes = Math.floor(totalBoxes * 0.35);
    const platoBoxes = Math.floor(totalBoxes * 0.25);
    const lamPlusBoxes = Math.floor(totalBoxes * 0.20);
    const lamDemiBoxes = Math.floor(totalBoxes * 0.15);
    const otherBoxes = totalBoxes - bigBoxes - platoBoxes - lamPlusBoxes - lamDemiBoxes;

    const totalDeposits = (bigBoxes + platoBoxes) * 10 + (lamPlusBoxes + lamDemiBoxes + otherBoxes) * 3;

    const detail = `
🧺 تقرير مفصل - إدارة الصناديق:

📊 إحصائيات عامة:
• إجمالي الصناديق: ${totalBoxes} صندوق
• إجمالي قيمة الرهون: ${totalDeposits} د
• متوسط الرهن للصندوق: ${(totalDeposits / totalBoxes).toFixed(2)} د

📦 توزيع أنواع الصناديق:
• صندوق كبير: ${bigBoxes} (35%) - رهن 10د
• Plato: ${platoBoxes} (25%) - رهن 10د
• Lam plus: ${lamPlusBoxes} (20%) - رهن 3د
• Lam demi: ${lamDemiBoxes} (15%) - رهن 3د
• أنواع أخرى: ${otherBoxes} (5%) - رهن 3د

💰 تحليل الرهون:
• رهون عالية (10د): ${bigBoxes + platoBoxes} صندوق = ${(bigBoxes + platoBoxes) * 10} د
• رهون منخفضة (3د): ${lamPlusBoxes + lamDemiBoxes + otherBoxes} صندوق = ${(lamPlusBoxes + lamDemiBoxes + otherBoxes) * 3} د
• نسبة الرهون العالية: ${(((bigBoxes + platoBoxes) / totalBoxes) * 100).toFixed(1)}%

⚖️ تحليل الأوزان:
• صناديق كبيرة: ${bigBoxes * 2.0} كغ (وزن فارغ)
• Plato: ${platoBoxes * 1.5} كغ (وزن فارغ)
• Lam plus: ${lamPlusBoxes * 0.75} كغ (وزن فارغ)
• Lam demi: ${lamDemiBoxes * 0.7} كغ (وزن فارغ)
• إجمالي وزن الصناديق الفارغة: ${(bigBoxes * 2.0 + platoBoxes * 1.5 + lamPlusBoxes * 0.75 + lamDemiBoxes * 0.7).toFixed(1)} كغ

📈 حالة المخزون:
• صناديق متاحة: ${Math.floor(totalBoxes * 0.15)} صندوق
• صناديق مستخدمة: ${Math.floor(totalBoxes * 0.85)} صندوق
• معدل الاستخدام: 85%
• حاجة لصناديق إضافية: ${totalBoxes < 150 ? 'نعم' : 'لا'}

🔄 حركة الصناديق:
• صناديق واردة اليوم: ${Math.floor(totalBoxes * 0.3)}
• صناديق صادرة اليوم: ${Math.floor(totalBoxes * 0.25)}
• صافي الحركة: +${Math.floor(totalBoxes * 0.05)} صندوق

💡 توصيات:
• زيادة مخزون الصناديق الكبيرة
• مراجعة نظام الرهون
• تحسين تتبع حركة الصناديق
• صيانة دورية للصناديق التالفة
    `;

    showNotification('🧺 تقرير الصناديق المفصل', detail, 'info');
}

function showGenericDetail(section, title, value) {
    const detail = `
📋 معلومات ${title}:
• القيمة الحالية: ${value}
• القسم: ${section}
• آخر تحديث: ${new Date().toLocaleTimeString('ar-TN')}
    `;

    showNotification(title, detail, 'info');
}

// Notification System
function showNotification(title, message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <h3>${title}</h3>
            <button class="notification-close">&times;</button>
        </div>
        <div class="notification-body">
            <pre>${message}</pre>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto hide after 8 seconds
    setTimeout(() => hideNotification(notification), 8000);

    // Close button
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

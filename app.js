// Global Variables
let currentSection = 'dashboard';

// Sample Data - سيتم استبدالها بقاعدة بيانات حقيقية لاحقاً
const clients = [
    { id: 1, name: "أحمد محمد", boxes: 12, debt: 500, deposit: 30 },
    { id: 2, name: "فاطمة علي", boxes: 8, debt: 0, deposit: 24 },
    { id: 3, name: "محمود حسن", boxes: 15, debt: 300, deposit: 45 },
    { id: 4, name: "زينب أحمد", boxes: 6, debt: 150, deposit: 18 },
    { id: 5, name: "عبد الله سالم", boxes: 20, debt: 0, deposit: 60 },
    { id: 6, name: "مريم خالد", boxes: 10, debt: 200, deposit: 30 },
    { id: 7, name: "يوسف عمر", boxes: 18, debt: 450, deposit: 54 },
    { id: 8, name: "نور الدين", boxes: 7, debt: 100, deposit: 21 }
];

// Dashboard Data
const dashboardData = {
    clientCount: clients.length,
    purchaseCount: 26,
    totalWeight: 545.6,
    totalSales: 3240.500,
    productCount: 12,
    totalDebt: clients.reduce((sum, client) => sum + client.debt, 0),
    totalBoxes: clients.reduce((sum, client) => sum + client.boxes, 0)
};

// DOM Content Loaded
document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupNavigation();
    loadCurrentDate();
    loadDashboardData();
    renderClients();
    setupEventListeners();
    showSection('dashboard');
}

// Setup Navigation
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const actionButtons = document.querySelectorAll('[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            updateActiveNav(this);
        });
    });
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
                updateActiveNavBySection(section);
            }
        });
    });
}

// Show Section
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.remove('visible');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('visible');
        currentSection = sectionName;
    }
}

// Update Active Navigation
function updateActiveNav(activeLink) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

function updateActiveNavBySection(sectionName) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === sectionName) {
            link.classList.add('active');
        }
    });
}

// Load Current Date
function loadCurrentDate() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    const dateString = now.toLocaleDateString('ar-TN', options);
    document.getElementById('currentDate').textContent = dateString;
}

// Load Dashboard Data
function loadDashboardData() {
    document.getElementById("clientCount").textContent = dashboardData.clientCount;
    document.getElementById("purchaseCount").textContent = dashboardData.purchaseCount;
    document.getElementById("totalWeight").textContent = dashboardData.totalWeight.toFixed(1);
    document.getElementById("totalSales").textContent = dashboardData.totalSales.toFixed(3);
    document.getElementById("productCount").textContent = dashboardData.productCount;
    document.getElementById("totalDebt").textContent = dashboardData.totalDebt.toFixed(3);
    document.getElementById("totalBoxes").textContent = dashboardData.totalBoxes;
    
    // Load alerts
    loadAlerts();
}

// Load Alerts
function loadAlerts() {
    const alertsContainer = document.getElementById('alertsContainer');
    const highDebtClients = clients.filter(client => client.debt > 400);
    
    if (highDebtClients.length > 0) {
        alertsContainer.innerHTML = '';
        highDebtClients.forEach(client => {
            const alert = document.createElement('div');
            alert.className = 'alert alert-warning';
            alert.innerHTML = `
                <span>⚠️ الحريف "${client.name}" لديه دين مرتفع: ${client.debt} د</span>
            `;
            alertsContainer.appendChild(alert);
        });
    } else {
        alertsContainer.innerHTML = '<p>لا توجد تنبيهات في الوقت الحالي</p>';
    }
}

// Clients Management
function renderClients(filteredClients = clients) {
    const tableBody = document.getElementById("clientsBody");
    if (!tableBody) return;
    
    tableBody.innerHTML = "";

    if (filteredClients.length === 0) {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td colspan="6" style="text-align: center; color: #64748b; padding: 20px;">
                لا توجد نتائج مطابقة للبحث
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    filteredClients.forEach(client => {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td>${client.id}</td>
            <td>${client.name}</td>
            <td>${client.boxes}</td>
            <td style="color: ${client.debt > 0 ? '#ef4444' : '#10b981'}">${client.debt} د</td>
            <td>${client.deposit} صندوق</td>
            <td>
                <button class="action-btn" onclick="viewClient(${client.id})">👁️ عرض</button>
                <button class="action-btn" onclick="editClient(${client.id})">✏️ تعديل</button>
                <button class="action-btn danger" onclick="deleteClient(${client.id})">🗑️ حذف</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// Search Clients
function searchClients(query) {
    const lowerQuery = query.toLowerCase().trim();
    if (lowerQuery === '') {
        renderClients(clients);
        return;
    }
    
    const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(lowerQuery) || 
        client.id.toString().includes(lowerQuery)
    );
    renderClients(filtered);
}

// Client Actions
function viewClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        showClientDetailedReport(client);
    }
}

function showClientDetailedReport(client) {
    // Calculate additional statistics
    const totalClients = clients.length;
    const clientRank = clients.sort((a, b) => b.debt - a.debt).findIndex(c => c.id === client.id) + 1;
    const avgDebt = clients.reduce((sum, c) => sum + c.debt, 0) / totalClients;
    const avgBoxes = clients.reduce((sum, c) => sum + c.boxes, 0) / totalClients;

    // Estimate deposit value (assuming average 5 dinars per box)
    const estimatedDepositValue = client.deposit * 5;

    // Client status
    const debtStatus = client.debt > 400 ? "مرتفع ⚠️" : client.debt > 100 ? "متوسط ⚡" : client.debt > 0 ? "منخفض ✅" : "لا يوجد ✅";
    const activityStatus = client.boxes > avgBoxes ? "نشط جداً 🔥" : client.boxes > 0 ? "نشط ✅" : "غير نشط ⚠️";

    const report = `
📊 تقرير مفصل - الحريف: ${client.name}

🆔 المعلومات الأساسية:
• رقم التعريف: ${client.id}
• اسم الحريف: ${client.name}
• تاريخ آخر تحديث: ${new Date().toLocaleDateString('ar-TN')}

📦 إحصائيات الصناديق:
• عدد الصناديق الحالي: ${client.boxes} صندوق
• متوسط الصناديق (عام): ${avgBoxes.toFixed(1)} صندوق
• الحالة: ${client.boxes > avgBoxes ? `أعلى من المتوسط بـ ${(client.boxes - avgBoxes).toFixed(1)} صندوق` : `أقل من المتوسط بـ ${(avgBoxes - client.boxes).toFixed(1)} صندوق`}
• مستوى النشاط: ${activityStatus}

💰 تحليل الديون:
• إجمالي الديون: ${client.debt} د
• متوسط الديون (عام): ${avgDebt.toFixed(2)} د
• حالة الدين: ${debtStatus}
• ترتيب الحريف: ${clientRank} من أصل ${totalClients} (حسب الديون)
${client.debt > 0 ? `• نسبة دين الحريف من الإجمالي: ${((client.debt / clients.reduce((sum, c) => sum + c.debt, 0)) * 100).toFixed(1)}%` : ''}

🧺 معلومات الرهون:
• عدد الصناديق المرهونة: ${client.deposit} صندوق
• القيمة التقديرية للرهون: ${estimatedDepositValue} د
• نسبة الرهون للصناديق: ${client.boxes > 0 ? ((client.deposit / client.boxes) * 100).toFixed(1) : 0}%

📈 تحليل الأداء:
• إجمالي التعاملات المقدرة: ${(client.boxes * 15).toFixed(0)} د
• متوسط قيمة الصندوق: ${client.boxes > 0 ? ((client.boxes * 15) / client.boxes).toFixed(2) : 0} د
• نسبة الدين للتعاملات: ${client.boxes > 0 ? ((client.debt / (client.boxes * 15)) * 100).toFixed(1) : 0}%

⚠️ تنبيهات وتوصيات:
${client.debt > 400 ? '• تحتاج متابعة فورية للديون المرتفعة' : ''}
${client.debt === 0 ? '• حريف ممتاز - لا توجد ديون' : ''}
${client.boxes === 0 ? '• حريف غير نشط - يحتاج تفعيل' : ''}
${client.deposit > client.boxes ? '• عدد الرهون أكبر من الصناديق - يحتاج مراجعة' : ''}
${client.boxes > 20 ? '• حريف كبير - يستحق اهتمام خاص' : ''}

📅 معلومات إضافية:
• آخر زيارة: اليوم
• عدد العمليات هذا الشهر: ${Math.floor(Math.random() * 10) + 1}
• تقييم الحريف: ${client.debt === 0 && client.boxes > avgBoxes ? 'ممتاز ⭐⭐⭐⭐⭐' : client.debt < 200 && client.boxes > 0 ? 'جيد ⭐⭐⭐⭐' : client.debt < 400 ? 'متوسط ⭐⭐⭐' : 'يحتاج متابعة ⭐⭐'}
    `;

    showNotification(`📊 تقرير الحريف: ${client.name}`, report, 'info');
}

function editClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        showEditClientModal(client);
    }
}

function showEditClientModal(client) {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';

    // Create modal content
    modalOverlay.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل بيانات الحريف</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editClientForm">
                    <div class="form-group">
                        <label for="editClientId">رقم التعريف:</label>
                        <input type="number" id="editClientId" value="${client.id}" min="1" required>
                    </div>

                    <div class="form-group">
                        <label for="editClientName">اسم الحريف:</label>
                        <input type="text" id="editClientName" value="${client.name}" required>
                    </div>

                    <div class="form-group">
                        <label for="editClientBoxes">عدد الصناديق:</label>
                        <input type="number" id="editClientBoxes" value="${client.boxes}" min="0" required>
                    </div>

                    <div class="form-group">
                        <label for="editClientDebt">الديون (د):</label>
                        <input type="number" id="editClientDebt" value="${client.debt}" min="0" step="0.001" required>
                    </div>

                    <div class="form-group">
                        <label for="editClientDeposit">الرهون (صندوق):</label>
                        <input type="number" id="editClientDeposit" value="${client.deposit}" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="cancelEdit">إلغاء</button>
                <button type="button" class="btn-primary" id="saveEdit">حفظ التعديلات</button>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.appendChild(modalOverlay);

    // Show modal with animation
    setTimeout(() => modalOverlay.classList.add('show'), 10);

    // Focus on first input
    document.getElementById('editClientName').focus();

    // Handle close modal
    function closeModal() {
        modalOverlay.classList.remove('show');
        setTimeout(() => {
            if (modalOverlay.parentNode) {
                modalOverlay.parentNode.removeChild(modalOverlay);
            }
        }, 300);
    }

    // Close button
    modalOverlay.querySelector('.modal-close').addEventListener('click', closeModal);

    // Cancel button
    document.getElementById('cancelEdit').addEventListener('click', closeModal);

    // Click outside to close
    modalOverlay.addEventListener('click', function(e) {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });

    // Save button
    document.getElementById('saveEdit').addEventListener('click', function() {
        const newId = parseInt(document.getElementById('editClientId').value);
        const newName = document.getElementById('editClientName').value.trim();
        const newBoxes = parseInt(document.getElementById('editClientBoxes').value);
        const newDebt = parseFloat(document.getElementById('editClientDebt').value);
        const newDeposit = parseInt(document.getElementById('editClientDeposit').value);

        // Validation
        if (!newName) {
            alert('يرجى إدخال اسم الحريف');
            return;
        }

        if (newId !== client.id && clients.find(c => c.id === newId)) {
            alert('رقم التعريف موجود مسبقاً، يرجى اختيار رقم آخر');
            return;
        }

        if (newBoxes < 0 || newDebt < 0 || newDeposit < 0) {
            alert('لا يمكن أن تكون القيم سالبة');
            return;
        }

        // Save changes
        const oldData = { ...client };
        client.id = newId;
        client.name = newName;
        client.boxes = newBoxes;
        client.debt = newDebt;
        client.deposit = newDeposit;

        // Update displays
        renderClients();
        updateDashboardData();

        // Show success message
        showNotification(
            '✅ تم التحديث بنجاح',
            `تم تحديث بيانات الحريف "${client.name}" بنجاح\n\nالتغييرات:\n${generateChangesSummary(oldData, client)}`,
            'success'
        );

        closeModal();
    });

    // Handle Enter key to save
    modalOverlay.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.target.tagName === 'INPUT') {
            document.getElementById('saveEdit').click();
        }
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function generateChangesSummary(oldData, newData) {
    const changes = [];

    if (oldData.id !== newData.id) {
        changes.push(`• الرقم: ${oldData.id} ← ${newData.id}`);
    }
    if (oldData.name !== newData.name) {
        changes.push(`• الاسم: ${oldData.name} ← ${newData.name}`);
    }
    if (oldData.boxes !== newData.boxes) {
        changes.push(`• الصناديق: ${oldData.boxes} ← ${newData.boxes}`);
    }
    if (oldData.debt !== newData.debt) {
        changes.push(`• الديون: ${oldData.debt} د ← ${newData.debt} د`);
    }
    if (oldData.deposit !== newData.deposit) {
        changes.push(`• الرهون: ${oldData.deposit} ← ${newData.deposit}`);
    }

    return changes.length > 0 ? changes.join('\n') : 'لا توجد تغييرات';
}

// Simple Notification System
function showNotification(title, message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <h3>${title}</h3>
            <button class="notification-close">&times;</button>
        </div>
        <div class="notification-body">
            <pre>${message}</pre>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto hide after 8 seconds
    setTimeout(() => hideNotification(notification), 8000);

    // Close button
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

function deleteClient(id) {
    const client = clients.find(c => c.id === id);
    if (client) {
        if (confirm(`هل أنت متأكد من حذف الحريف "${client.name}"؟`)) {
            const index = clients.findIndex(c => c.id === id);
            clients.splice(index, 1);
            renderClients();
            updateDashboardData();
            alert("تم حذف الحريف بنجاح");
        }
    }
}

function addNewClient() {
    const name = prompt("اسم الحريف الجديد:");
    if (name && name.trim() !== '') {
        const newId = Math.max(...clients.map(c => c.id)) + 1;
        const newClient = {
            id: newId,
            name: name.trim(),
            boxes: 0,
            debt: 0,
            deposit: 0
        };
        clients.push(newClient);
        renderClients();
        updateDashboardData();
        alert("تم إضافة الحريف بنجاح");
    }
}

// Update Dashboard Data
function updateDashboardData() {
    dashboardData.clientCount = clients.length;
    dashboardData.totalDebt = clients.reduce((sum, client) => sum + client.debt, 0);
    dashboardData.totalBoxes = clients.reduce((sum, client) => sum + client.boxes, 0);
    
    if (currentSection === 'dashboard') {
        loadDashboardData();
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            searchClients(e.target.value);
        });
    }

    // Add client button
    const addClientBtn = document.getElementById('addClientBtn');
    if (addClientBtn) {
        addClientBtn.addEventListener('click', addNewClient);
    }

    // Change date button
    const changeDateBtn = document.getElementById('changeDateBtn');
    if (changeDateBtn) {
        changeDateBtn.addEventListener('click', function() {
            alert("وظيفة تغيير التاريخ - قيد التطوير");
        });
    }


}




